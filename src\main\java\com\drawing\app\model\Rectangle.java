package com.drawing.app.model;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

public class Rectangle extends Shape {
    private double width, height;
    
    public Rectangle(double x, double y, double width, double height, Color color, double strokeWidth) {
        super(x, y, color, strokeWidth);
        this.width = Math.abs(width);
        this.height = Math.abs(height);
    }
    
    @Override
    protected void draw(GraphicsContext gc) {
        gc.strokeRect(x, y, width, height);
    }
      @Override
    public boolean contains(double px, double py) {
        return px >= x && px <= x + width && py >= y && py <= y + height;
    }
    
    @Override
    public double getCenterX() {
        return x + width / 2;
    }
    
    @Override
    public double getCenterY() {
        return y + height / 2;
    }
    
    @Override
    protected void drawSelectionHandles(GraphicsContext gc) {
        gc.setStroke(Color.BLUE);
        gc.setLineWidth(1);
        
        // Draw selection rectangle
        gc.strokeRect(x - 2, y - 2, width + 4, height + 4);
        
        // Draw corner handles
        double handleSize = 6;
        gc.setFill(Color.BLUE);
        
        // Top-left
        gc.fillRect(x - handleSize/2, y - handleSize/2, handleSize, handleSize);
        // Top-right
        gc.fillRect(x + width - handleSize/2, y - handleSize/2, handleSize, handleSize);
        // Bottom-left
        gc.fillRect(x - handleSize/2, y + height - handleSize/2, handleSize, handleSize);
        // Bottom-right
        gc.fillRect(x + width - handleSize/2, y + height - handleSize/2, handleSize, handleSize);
        
        // Rotation handle
        gc.setFill(Color.GREEN);
        gc.fillOval(getCenterX() - handleSize/2, y - 20 - handleSize/2, handleSize, handleSize);
        gc.strokeLine(getCenterX(), y, getCenterX(), y - 20);
    }
    
    public void updateSize(double newWidth, double newHeight) {
        this.width = Math.abs(newWidth);
        this.height = Math.abs(newHeight);
    }
    
    public double getWidth() { return width; }
    public double getHeight() { return height; }
    public void setWidth(double width) { this.width = Math.abs(width); }
    public void setHeight(double height) { this.height = Math.abs(height); }
}
