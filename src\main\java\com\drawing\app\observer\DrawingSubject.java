package com.drawing.app.observer;

import java.util.ArrayList;
import java.util.List;

public class DrawingSubject {
    private List<DrawingObserver> observers = new ArrayList<>();
    
    public void addObserver(DrawingObserver observer) {
        observers.add(observer);
    }
    
    public void removeObserver(DrawingObserver observer) {
        observers.remove(observer);
    }
    
    protected void notifyShapeSelected(String shapeType) {
        for (DrawingObserver observer : observers) {
            observer.onShapeSelected(shapeType);
        }
    }
    
    protected void notifyShapeDrawn(String shapeInfo) {
        for (DrawingObserver observer : observers) {
            observer.onShapeDrawn(shapeInfo);
        }
    }
    
    protected void notifyDrawingSaved(String filename) {
        for (DrawingObserver observer : observers) {
            observer.onDrawingSaved(filename);
        }
    }
    
    protected void notifyDrawingLoaded(String filename) {
        for (DrawingObserver observer : observers) {
            observer.onDrawingLoaded(filename);
        }
    }
}
