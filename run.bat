@echo off

REM Download JavaFX if not present
if not exist "javafx-sdk" (
    echo Downloading JavaFX SDK...
    powershell -Command "Invoke-WebRequest -Uri 'https://download2.gluonhq.com/openjfx/17.0.2/openjfx-17.0.2_windows-x64_bin-sdk.zip' -OutFile 'javafx-sdk.zip'"
    powershell -Command "Expand-Archive -Path 'javafx-sdk.zip' -DestinationPath '.'"
    ren javafx-sdk-17.0.2 javafx-sdk
    del javafx-sdk.zip
)

REM Download MySQL Connector if not present
if not exist "mysql-connector-java-8.0.33.jar" (
    echo Downloading MySQL Connector...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar' -OutFile 'mysql-connector-java-8.0.33.jar'"
)

REM Create target/classes directory if it doesn't exist
if not exist "target\classes" mkdir target\classes

REM Compile the project with MySQL driver in classpath
echo Compiling project...
javac --module-path javafx-sdk/lib --add-modules javafx.controls,javafx.fxml -cp "src/main/java;mysql-connector-java-8.0.33.jar" -d target/classes src/main/java/com/drawing/app/Main.java

REM Run with JavaFX modules and MySQL driver
echo Starting application...
java --module-path javafx-sdk/lib --add-modules javafx.controls,javafx.fxml -cp "target/classes;mysql-connector-java-8.0.33.jar" com.drawing.app.Main
