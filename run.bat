@echo off

REM Download JavaFX if not present
if not exist "javafx-sdk" (
    echo Downloading JavaFX SDK...
    powershell -Command "Invoke-WebRequest -Uri 'https://download2.gluonhq.com/openjfx/17.0.2/openjfx-17.0.2_windows-x64_bin-sdk.zip' -OutFile 'javafx-sdk.zip'"
    powershell -Command "Expand-Archive -Path 'javafx-sdk.zip' -DestinationPath '.'"
    ren javafx-sdk-17.0.2 javafx-sdk
    del javafx-sdk.zip
)

REM Download MySQL Connector if not present
if not exist "mysql-connector-java-8.0.33.jar" (
    echo Downloading MySQL Connector...
    powershell -Command "try { Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar' -OutFile 'mysql-connector-java-8.0.33.jar' -UseBasicParsing } catch { Write-Host 'Download failed, continuing without MySQL driver' }"
)

REM Create target/classes directory if it doesn't exist
if not exist "target\classes" mkdir target\classes

REM Compile the project
echo Compiling project...
javac --module-path javafx-sdk/lib --add-modules javafx.controls,javafx.fxml -cp "src/main/java" -d target/classes src/main/java/com/drawing/app/Main.java src/main/java/com/drawing/app/controller/*.java src/main/java/com/drawing/app/model/*.java src/main/java/com/drawing/app/view/*.java src/main/java/com/drawing/app/persistence/*.java src/main/java/com/drawing/app/command/*.java src/main/java/com/drawing/app/factory/*.java src/main/java/com/drawing/app/logging/*.java src/main/java/com/drawing/app/observer/*.java 2>nul

REM Run the application
echo Starting application...
java --module-path javafx-sdk/lib --add-modules javafx.controls,javafx.fxml -cp "target/classes" com.drawing.app.Main
