@echo off

REM Download JavaFX if not present
if not exist "javafx-sdk" (
    echo Downloading JavaFX SDK...
    powershell -Command "Invoke-WebRequest -Uri 'https://download2.gluonhq.com/openjfx/17.0.2/openjfx-17.0.2_windows-x64_bin-sdk.zip' -OutFile 'javafx-sdk.zip'"
    powershell -Command "Expand-Archive -Path 'javafx-sdk.zip' -DestinationPath '.'"
    ren javafx-sdk-17.0.2 javafx-sdk
    del javafx-sdk.zip
)

REM Compile the project
mvn clean compile

REM Run with JavaFX modules
java --module-path javafx-sdk/lib --add-modules javafx.controls,javafx.fxml -cp target/classes com.drawing.app.Main
