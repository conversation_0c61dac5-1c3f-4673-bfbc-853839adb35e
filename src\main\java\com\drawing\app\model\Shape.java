package com.drawing.app.model;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

// Strategy Pattern - Abstract Shape
public abstract class Shape {
    protected double x, y;
    protected Color color;
    protected double strokeWidth;
    protected double rotation = 0; // Rotation angle in degrees
    protected boolean selected = false;
    
    public Shape(double x, double y, Color color, double strokeWidth) {
        this.x = x;
        this.y = y;
        this.color = color;
        this.strokeWidth = strokeWidth;
    }
    
    // Template Method Pattern
    public final void render(GraphicsContext gc) {
        gc.save();
        
        // Apply rotation
        if (rotation != 0) {
            gc.translate(getCenterX(), getCenterY());
            gc.rotate(rotation);
            gc.translate(-getCenterX(), -getCenterY());
        }
        
        gc.setStroke(color);
        gc.setLineWidth(strokeWidth);
        draw(gc);
        
        // Draw selection handles if selected
        if (selected) {
            drawSelectionHandles(gc);
        }
        
        gc.restore();
    }
      protected abstract void draw(GraphicsContext gc);
    public abstract boolean contains(double x, double y);
    public abstract double getCenterX();
    public abstract double getCenterY();
    protected abstract void drawSelectionHandles(GraphicsContext gc);
    
    // Getters and setters
    public double getX() { return x; }
    public double getY() { return y; }
    public Color getColor() { return color; }
    public double getStrokeWidth() { return strokeWidth; }
    public double getRotation() { return rotation; }
    public boolean isSelected() { return selected; }
    
    public void setX(double x) { this.x = x; }
    public void setY(double y) { this.y = y; }
    public void setColor(Color color) { this.color = color; }
    public void setStrokeWidth(double strokeWidth) { this.strokeWidth = strokeWidth; }
    public void setRotation(double rotation) { this.rotation = rotation; }
    public void setSelected(boolean selected) { this.selected = selected; }
    
    public void move(double deltaX, double deltaY) {
        this.x += deltaX;
        this.y += deltaY;
    }
    
    public void rotate(double deltaAngle) {
        this.rotation += deltaAngle;
        this.rotation = this.rotation % 360;
    }
}
