package com.drawing.app.logging;

import com.drawing.app.observer.DrawingObserver;

// Context class for Strategy Pattern
public class Logger implements DrawingObserver {
    private LoggingStrategy strategy;
    
    public Logger(LoggingStrategy strategy) {
        this.strategy = strategy;
    }
    
    public void setStrategy(LoggingStrategy strategy) {
        if (this.strategy != null) {
            this.strategy.close();
        }
        this.strategy = strategy;
    }
    
    @Override
    public void onShapeSelected(String shapeType) {
        strategy.log("Shape selected: " + shapeType);
    }
    
    @Override
    public void onShapeDrawn(String shapeInfo) {
        strategy.log("Shape drawn: " + shapeInfo);
    }
    
    @Override
    public void onDrawingSaved(String filename) {
        strategy.log("Drawing saved: " + filename);
    }
    
    @Override
    public void onDrawingLoaded(String filename) {
        strategy.log("Drawing loaded: " + filename);
    }
    
    public void close() {
        if (strategy != null) {
            strategy.close();
        }
    }
}
