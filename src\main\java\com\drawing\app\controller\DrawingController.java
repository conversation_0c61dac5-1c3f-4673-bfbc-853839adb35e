package com.drawing.app.controller;

import com.drawing.app.command.CommandManager;
import com.drawing.app.command.DrawShapeCommand;
import com.drawing.app.factory.ShapeFactory;
import com.drawing.app.logging.ConsoleLoggingStrategy;
import com.drawing.app.logging.DatabaseLoggingStrategy;
import com.drawing.app.logging.FileLoggingStrategy;
import com.drawing.app.logging.Logger;
import com.drawing.app.logging.LoggingStrategy;
import com.drawing.app.model.Drawing;
import com.drawing.app.observer.DrawingSubject;
import com.drawing.app.view.DrawingCanvas;
import com.drawing.app.view.MainView;

import javafx.scene.control.Button;
import javafx.scene.control.ColorPicker;
import javafx.scene.control.Menu;
import javafx.scene.control.MenuBar;
import javafx.scene.control.RadioButton;
import javafx.scene.control.RadioMenuItem;
import javafx.scene.control.ScrollPane;
import javafx.scene.control.Slider;
import javafx.scene.control.ToolBar;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;

public class DrawingController extends DrawingSubject {
    private final MainView view;
    private final Drawing currentDrawing;
    private final CommandManager commandManager;
    private final Logger logger;
    private ShapeFactory.ShapeType selectedShapeType;
    private Color selectedColor;
    private double selectedStrokeWidth;

    public DrawingController(MainView view) {
        this.view = view;
        this.currentDrawing = new Drawing();
        this.commandManager = new CommandManager();
        this.selectedShapeType = ShapeFactory.ShapeType.RECTANGLE;
        this.selectedColor = Color.BLACK;
        this.selectedStrokeWidth = 2.0;
        
        // Initialize with console logging
        this.logger = new Logger(new ConsoleLoggingStrategy());
        addObserver(logger);
        
        setupEventHandlers();
        setupKeyboardShortcuts();
    }

    private void setupEventHandlers() {
        // Setup canvas
        DrawingCanvas canvas = view.getCanvas();
        canvas.setCurrentShapeType(selectedShapeType);
        canvas.setCurrentColor(selectedColor);
        canvas.setCurrentStrokeWidth(selectedStrokeWidth);
          // Set up shape creation callback
        canvas.setShapeCreationCallback(shape -> {
            DrawShapeCommand command = new DrawShapeCommand(currentDrawing, shape);
            commandManager.executeCommand(command);
            updateCanvas(); // Ensure canvas reflects the new state
            notifyShapeDrawn(shape.getClass().getSimpleName() + " created at (" + 
                           shape.getX() + ", " + shape.getY() + ")");
        });
        
        // Setup menu events
        setupMenuEvents(view.getMenuBar());
        
        // Setup toolbar events
        setupToolbarEvents(view.getToolBar());
        
        // Setup shape palette events
        setupPaletteEvents(view.getShapePalette());
        
        // Setup properties panel events
        setupPropertiesEvents(view.getPropertiesPanel());
    }
    
    private void setupKeyboardShortcuts() {
        view.getRoot().setOnKeyPressed(this::handleKeyPressed);
        view.getRoot().setFocusTraversable(true);
        view.getRoot().requestFocus();
    }
      private void handleKeyPressed(KeyEvent event) {
        if (event.getCode() == KeyCode.DELETE || event.getCode() == KeyCode.BACK_SPACE) {
            view.getCanvas().deleteSelectedShape();
            notifyShapeDrawn("Shape deleted");
        } else if (event.isControlDown()) {
            switch (event.getCode()) {
                case Z:
                    undo();
                    break;
                case Y:
                    redo();
                    break;
                case N:
                    newDrawing();
                    break;
                case S:
                    saveDrawing();
                    break;
                case O:
                    openDrawing();
                    break;
                default:
                    break;
            }
        }
    }

    public void selectShape(ShapeFactory.ShapeType shapeType) {
        this.selectedShapeType = shapeType;
        view.getCanvas().setCurrentShapeType(shapeType);
        notifyShapeSelected(shapeType.toString());
    }

    public void selectColor(Color color) {
        this.selectedColor = color;
        view.getCanvas().setCurrentColor(color);
    }

    public void selectStrokeWidth(double width) {
        this.selectedStrokeWidth = width;
        view.getCanvas().setCurrentStrokeWidth(width);
    }

    public void undo() {
        commandManager.undo();
        updateCanvas();
        notifyShapeDrawn("Undo performed");
    }

    public void redo() {
        commandManager.redo();
        updateCanvas();
        notifyShapeDrawn("Redo performed");
    }

    public void clearDrawing() {
        currentDrawing.clear();
        updateCanvas();
        notifyShapeDrawn("Drawing cleared");
    }

    private void setupMenuEvents(MenuBar menuBar) {
        // File menu events
        Menu fileMenu = menuBar.getMenus().get(0);
        fileMenu.getItems().get(0).setOnAction(e -> newDrawing()); // New
        fileMenu.getItems().get(1).setOnAction(e -> openDrawing()); // Open
        fileMenu.getItems().get(3).setOnAction(e -> saveDrawing()); // Save
        fileMenu.getItems().get(4).setOnAction(e -> saveAsDrawing()); // Save As
        fileMenu.getItems().get(6).setOnAction(e -> System.exit(0)); // Exit
        
        // Edit menu events
        Menu editMenu = menuBar.getMenus().get(1);
        editMenu.getItems().get(0).setOnAction(e -> undo()); // Undo
        editMenu.getItems().get(1).setOnAction(e -> redo()); // Redo
        editMenu.getItems().get(3).setOnAction(e -> clearDrawing()); // Clear
        
        // Logging menu events
        Menu loggingMenu = menuBar.getMenus().get(2);
        ((RadioMenuItem) loggingMenu.getItems().get(0)).setOnAction(e -> setLoggingStrategy("console"));
        ((RadioMenuItem) loggingMenu.getItems().get(1)).setOnAction(e -> setLoggingStrategy("file"));
        ((RadioMenuItem) loggingMenu.getItems().get(2)).setOnAction(e -> setLoggingStrategy("database"));
    }

    private void setupToolbarEvents(ToolBar toolBar) {
        ((Button) toolBar.getItems().get(0)).setOnAction(e -> newDrawing()); // New
        ((Button) toolBar.getItems().get(1)).setOnAction(e -> openDrawing()); // Open
        ((Button) toolBar.getItems().get(2)).setOnAction(e -> saveDrawing()); // Save
        ((Button) toolBar.getItems().get(4)).setOnAction(e -> undo()); // Undo
        ((Button) toolBar.getItems().get(5)).setOnAction(e -> redo()); // Redo
        ((Button) toolBar.getItems().get(7)).setOnAction(e -> clearDrawing()); // Clear
    }

    private void setupPaletteEvents(VBox palette) {
        ((RadioButton) palette.getChildren().get(2)).setOnAction(e -> selectShape(ShapeFactory.ShapeType.RECTANGLE));
        ((RadioButton) palette.getChildren().get(3)).setOnAction(e -> selectShape(ShapeFactory.ShapeType.CIRCLE));
        ((RadioButton) palette.getChildren().get(4)).setOnAction(e -> selectShape(ShapeFactory.ShapeType.LINE));
    }    private void setupPropertiesEvents(VBox wrapper) {
        // Look for the ScrollPane and get its content
        ScrollPane scrollPane = (ScrollPane) wrapper.getChildren().get(0);
        VBox properties = (VBox) scrollPane.getContent();
        
        // Find our components using their parent VBoxes
        VBox colorSection = (VBox) properties.getChildren().get(2);
        ColorPicker colorPicker = (ColorPicker) colorSection.lookup("#colorPicker");
        colorPicker.setOnAction(e -> setSelectedColor(colorPicker.getValue()));
        
        VBox strokeSection = (VBox) properties.getChildren().get(3);
        Slider strokeSlider = (Slider) strokeSection.lookup("#strokeSlider");
        strokeSlider.valueProperty().addListener((obs, oldVal, newVal) -> 
            setSelectedStrokeWidth(newVal.doubleValue()));
    }

    // Helper methods
    public void newDrawing() {
        currentDrawing.clear();
        updateCanvas();
        notifyDrawingSaved("New drawing created");
    }

    public void openDrawing() {
        // Implementation for opening drawing from database
        System.out.println("Open drawing functionality - to be implemented");
        notifyDrawingLoaded("Drawing opened");
    }

    public void saveDrawing() {
        if (currentDrawing.getName().equals("Untitled")) {
            saveAsDrawing();
        } else {
            saveDrawing(currentDrawing.getName());
        }
    }

    public void saveAsDrawing() {
        // Implementation for save as dialog
        System.out.println("Save As functionality - to be implemented");
        notifyDrawingSaved("Drawing saved as new file");
    }

    private void saveDrawing(String filename) {
        // Implementation for saving to database
        System.out.println("Saving drawing: " + filename);
        notifyDrawingSaved(filename);
    }

    public void addShape(Shape shape) {
        currentDrawing.addShape(shape);
        updateCanvas();
        notifyShapeDrawn(shape.getClass().getSimpleName() + " added programmatically");
    }

    public Drawing getCurrentDrawing() {
        return currentDrawing;
    }

    public void updateCanvas() {
        view.getCanvas().setDrawing(currentDrawing);
    }

    public void setLoggingStrategy(String strategyType) {
        LoggingStrategy strategy;
        switch (strategyType.toLowerCase()) {
            case "console":
                strategy = new ConsoleLoggingStrategy();
                break;
            case "file":
                strategy = new FileLoggingStrategy("drawing_log.txt");
                break;
            case "database":
                strategy = new DatabaseLoggingStrategy(
                    "***************************************", 
                    "root", 
                    "password"
                );
                break;
            default:
                strategy = new ConsoleLoggingStrategy();
        }
        logger.setStrategy(strategy);
    }
}
