package com.drawing.app.view;

import java.util.List;

import com.drawing.app.controller.GraphController;
import com.drawing.app.model.Node;

import javafx.geometry.Insets;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.TextArea;
import javafx.scene.control.TextField;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.VBox;

public class GraphPanel extends VBox {
    private GraphController graphController;
    private TextField startNodeField;
    private TextField endNodeField;
    private ComboBox<String> algorithmComboBox;
    private Button calculateButton;
    private TextArea resultArea;
    
    public GraphPanel(GraphController graphController) {
        this.graphController = graphController;
        setPadding(new Insets(10));
        setSpacing(10);
        
        Label titleLabel = new Label("Graph Operations");
        titleLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");
        
        // Node creation controls
        GridPane nodeCreationPane = new GridPane();
        nodeCreationPane.setHgap(5);
        nodeCreationPane.setVgap(5);
        
        Label nodeLabel = new Label("Create Node:");
        TextField nodeLabelField = new TextField();
        nodeLabelField.setPromptText("Node Label");
        Button createNodeButton = new Button("Create Node");
        createNodeButton.setOnAction(e -> {
            // In a real app, you'd get coordinates from canvas click
            double x = 100 + Math.random() * 300;
            double y = 100 + Math.random() * 200;
            graphController.createNode(x, y, nodeLabelField.getText());
            nodeLabelField.clear();
        });
        
        nodeCreationPane.add(nodeLabel, 0, 0);
        nodeCreationPane.add(nodeLabelField, 1, 0);
        nodeCreationPane.add(createNodeButton, 2, 0);
        
        // Edge creation controls
        GridPane edgeCreationPane = new GridPane();
        edgeCreationPane.setHgap(5);
        edgeCreationPane.setVgap(5);
        
        Label edgeLabel = new Label("Create Edge:");
        TextField startField = new TextField();
        startField.setPromptText("Start Node");
        TextField endField = new TextField();
        endField.setPromptText("End Node");
        TextField weightField = new TextField();
        weightField.setPromptText("Weight");
        Button createEdgeButton = new Button("Create Edge");
        createEdgeButton.setOnAction(e -> {
            Node start = graphController.getGraph().findNodeByLabel(startField.getText());
            Node end = graphController.getGraph().findNodeByLabel(endField.getText());
            
            if (start != null && end != null) {
                try {
                    double weight = Double.parseDouble(weightField.getText());
                    graphController.createEdge(start, end, weight);
                    startField.clear();
                    endField.clear();
                    weightField.clear();
                } catch (NumberFormatException ex) {
                    showAlert("Invalid weight. Please enter a number.");
                }
            } else {
                showAlert("One or both nodes not found.");
            }
        });
        
        edgeCreationPane.add(edgeLabel, 0, 0);
        edgeCreationPane.add(startField, 1, 0);
        edgeCreationPane.add(endField, 2, 0);
        edgeCreationPane.add(weightField, 3, 0);
        edgeCreationPane.add(createEdgeButton, 4, 0);
        
        // Shortest path controls
        GridPane shortestPathPane = new GridPane();
        shortestPathPane.setHgap(5);
        shortestPathPane.setVgap(5);
        
        Label pathLabel = new Label("Shortest Path:");
        startNodeField = new TextField();
        startNodeField.setPromptText("Start Node");
        endNodeField = new TextField();
        endNodeField.setPromptText("End Node");
        
        algorithmComboBox = new ComboBox<>();
        algorithmComboBox.getItems().addAll("Dijkstra");
        algorithmComboBox.setValue("Dijkstra");
        
        calculateButton = new Button("Calculate");
        calculateButton.setOnAction(e -> calculateShortestPath());
        
        shortestPathPane.add(pathLabel, 0, 0);
        shortestPathPane.add(startNodeField, 1, 0);
        shortestPathPane.add(endNodeField, 2, 0);
        shortestPathPane.add(algorithmComboBox, 3, 0);
        shortestPathPane.add(calculateButton, 4, 0);
        
        // Results area
        resultArea = new TextArea();
        resultArea.setEditable(false);
        resultArea.setPrefHeight(100);
        
        // Add all components
        getChildren().addAll(
            titleLabel,
            nodeCreationPane,
            edgeCreationPane,
            shortestPathPane,
            new Label("Results:"),
            resultArea
        );
    }
    
    private void calculateShortestPath() {
        String startLabel = startNodeField.getText();
        String endLabel = endNodeField.getText();
        
        if (startLabel.isEmpty() || endLabel.isEmpty()) {
            showAlert("Please enter both start and end nodes.");
            return;
        }
        
        List<Node> path = graphController.findShortestPath(startLabel, endLabel);
        
        if (path.isEmpty()) {
            resultArea.setText("No path found between " + startLabel + " and " + endLabel);
        } else {
            StringBuilder result = new StringBuilder("Shortest path: ");
            double totalDistance = 0;
            
            for (int i = 0; i < path.size(); i++) {
                result.append(path.get(i).getLabel());
                
                if (i < path.size() - 1) {
                    // Find the edge between these nodes
                    for (var edge : path.get(i).getEdges()) {
                        if (edge.getOtherNode(path.get(i)) == path.get(i + 1)) {
                            totalDistance += edge.getWeight();
                            result.append(" --(").append(edge.getWeight()).append(")--> ");
                            break;
                        }
                    }
                }
            }
            
            result.append("\nTotal distance: ").append(totalDistance);
            resultArea.setText(result.toString());
            
            // Highlight the path on the canvas
            graphController.highlightPath(path);
        }
    }
    
    private void showAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("Warning");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}

