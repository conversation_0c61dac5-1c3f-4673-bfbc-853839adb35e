package com.drawing.app.logging;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.LocalDateTime;

public class DatabaseLoggingStrategy implements LoggingStrategy {
    private Connection connection;
    private PreparedStatement insertStatement;
    private String dbUrl;
    private String username;
    private String password;
    
    public DatabaseLoggingStrategy(String dbUrl, String username, String password) {
        this.dbUrl = dbUrl;
        this.username = username;
        this.password = password;
        try {
            connection = DriverManager.getConnection(dbUrl, username, password);
            createTableIfNotExists();
            insertStatement = connection.prepareStatement(
                "INSERT INTO logs (timestamp, message) VALUES (?, ?)"
            );
        } catch (SQLException e) {
            System.err.println("Error creating database logger: " + e.getMessage());
        }
    }
    
    private void createTableIfNotExists() throws SQLException {
        String createTableSQL = "CREATE TABLE IF NOT EXISTS logs ("
            + "id INT AUTO_INCREMENT PRIMARY KEY,"
            + "timestamp DATETIME,"
            + "message TEXT"
            + ")";
        try (Statement stmt = connection.createStatement()) {
            stmt.execute(createTableSQL);
        }
    }
    
    @Override
    public void log(String message) {
        if (insertStatement != null) {
            try {
                insertStatement.setTimestamp(1, Timestamp.valueOf(LocalDateTime.now()));
                insertStatement.setString(2, message);
                insertStatement.executeUpdate();
            } catch (SQLException e) {
                System.err.println("Error logging to database: " + e.getMessage());
            }
        }
    }
    
    @Override
    public void close() {
        try {
            if (insertStatement != null) insertStatement.close();
            if (connection != null) connection.close();
        } catch (SQLException e) {
            System.err.println("Error closing database connection: " + e.getMessage());
        }
    }
}
