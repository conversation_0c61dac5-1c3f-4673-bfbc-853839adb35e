package com.drawing.app.view;

import com.drawing.app.controller.DrawingController;
import com.drawing.app.controller.GraphController;
import com.drawing.app.factory.ShapeFactory;

import javafx.geometry.Insets;
import javafx.geometry.Orientation;
import javafx.scene.control.Button;
import javafx.scene.control.ColorPicker;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.ScrollPane;
import javafx.scene.control.Separator;
import javafx.scene.control.Slider;
import javafx.scene.control.Tab;
import javafx.scene.control.TabPane;
import javafx.scene.control.ToggleButton;
import javafx.scene.control.ToggleGroup;
import javafx.scene.control.ToolBar;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;

public class MainView {
    private BorderPane root;
    private DrawingCanvas canvas;
    private DrawingController drawingController;
    private GraphController graphController;
    private GraphPanel graphPanel;
    
    public MainView() {
        root = new BorderPane();
        canvas = new DrawingCanvas(800, 600);
        
        drawingController = new DrawingController(this);
        graphController = new GraphController(drawingController);
        
        setupToolbar();
        setupStatusBar();
        setupCanvas();
        setupGraphPanel();
    }
    
    private void setupToolbar() {
        ToolBar toolbar = new ToolBar();
        
        // Shape selection buttons
        ToggleGroup shapeGroup = new ToggleGroup();
        
        ToggleButton rectButton = new ToggleButton("Rectangle");
        rectButton.setToggleGroup(shapeGroup);
        rectButton.setSelected(true);
        rectButton.setOnAction(e -> drawingController.selectShape(ShapeFactory.ShapeType.RECTANGLE));
        
        ToggleButton circleButton = new ToggleButton("Circle");
        circleButton.setToggleGroup(shapeGroup);
        circleButton.setOnAction(e -> drawingController.selectShape(ShapeFactory.ShapeType.CIRCLE));
        
        ToggleButton lineButton = new ToggleButton("Line");
        lineButton.setToggleGroup(shapeGroup);
        lineButton.setOnAction(e -> drawingController.selectShape(ShapeFactory.ShapeType.LINE));
        
        // Color picker
        ColorPicker colorPicker = new ColorPicker(Color.BLACK);
        colorPicker.setOnAction(e -> drawingController.selectColor(colorPicker.getValue()));
        
        // Stroke width slider
        Label strokeLabel = new Label("Stroke:");
        Slider strokeSlider = new Slider(1, 10, 2);
        strokeSlider.setShowTickMarks(true);
        strokeSlider.setShowTickLabels(true);
        strokeSlider.setMajorTickUnit(1);
        strokeSlider.setBlockIncrement(1);
        strokeSlider.valueProperty().addListener((obs, oldVal, newVal) -> 
            drawingController.selectStrokeWidth(newVal.doubleValue()));
        
        // Logging strategy selection
        Label loggingLabel = new Label("Logging:");
        ComboBox<String> loggingComboBox = new ComboBox<>();
        loggingComboBox.getItems().addAll("Console", "File", "Database");
        loggingComboBox.setValue("Console");
        loggingComboBox.setOnAction(e -> 
            drawingController.setLoggingStrategy(loggingComboBox.getValue().toLowerCase()));
        
        // File operations
        Button newButton = new Button("New");
        newButton.setOnAction(e -> drawingController.newDrawing());
        
        Button openButton = new Button("Open");
        openButton.setOnAction(e -> drawingController.openDrawing());
        
        Button saveButton = new Button("Save");
        saveButton.setOnAction(e -> drawingController.saveDrawing());
        
        Button saveAsButton = new Button("Save As");
        saveAsButton.setOnAction(e -> drawingController.saveAsDrawing());
        
        // Add all controls to toolbar
        toolbar.getItems().addAll(
            rectButton, circleButton, lineButton,
            new Separator(Orientation.VERTICAL),
            new Label("Color:"), colorPicker,
            new Separator(Orientation.VERTICAL),
            strokeLabel, strokeSlider,
            new Separator(Orientation.VERTICAL),
            loggingLabel, loggingComboBox,
            new Separator(Orientation.VERTICAL),
            newButton, openButton, saveButton, saveAsButton
        );
        
        root.setTop(toolbar);
    }
    
    private void setupStatusBar() {
        HBox statusBar = new HBox();
        statusBar.setPadding(new Insets(5));
        statusBar.setStyle("-fx-background-color: #f0f0f0; -fx-border-color: #cccccc; -fx-border-width: 1 0 0 0;");
        
        Label statusLabel = new Label("Ready");
        statusBar.getChildren().add(statusLabel);
        
        root.setBottom(statusBar);
    }
    
    private void setupCanvas() {
        ScrollPane scrollPane = new ScrollPane(canvas);
        scrollPane.setFitToWidth(true);
        scrollPane.setFitToHeight(true);
        root.setCenter(scrollPane);
    }
    
    private void setupGraphPanel() {
        graphPanel = new GraphPanel(graphController);
        
        // Create a tab pane for the right side
        TabPane tabPane = new TabPane();
        
        Tab propertiesTab = new Tab("Properties");
        propertiesTab.setClosable(false);
        propertiesTab.setContent(new VBox(new Label("Properties Panel")));
        
        Tab graphTab = new Tab("Graph");
        graphTab.setClosable(false);
        graphTab.setContent(graphPanel);
        
        tabPane.getTabs().addAll(propertiesTab, graphTab);
        
        root.setRight(tabPane);
    }
    
    public BorderPane getRoot() {
        return root;
    }
    
    public DrawingCanvas getCanvas() {
        return canvas;
    }
}
