package com.drawing.app.controller;

import java.util.List;

import com.drawing.app.model.Edge;
import com.drawing.app.model.Graph;
import com.drawing.app.model.Node;  // Correct import for Shape
import com.drawing.app.model.Shape;

import javafx.scene.paint.Color;

public class GraphController {
    private Graph graph = new Graph();
    private DrawingController drawingController;
    
    public GraphController(DrawingController drawingController) {
        this.drawingController = drawingController;
    }
    
    public void createNode(double x, double y, String label) {
        Node node = new Node(x, y, 20, Color.BLUE, 2, label);
        graph.addNode(node);
        drawingController.addShape((Shape)node);
    }
    
    public void createEdge(Node start, Node end, double weight) {
        Edge edge = new Edge(start, end, Color.BLACK, 1, weight);
        graph.addEdge(edge);
        drawingController.addShape((Shape)edge);
    }
    
    public List<Node> findShortestPath(String startLabel, String endLabel) {
        Node start = graph.findNodeByLabel(startLabel);
        Node end = graph.findNodeByLabel(endLabel);
        
        if (start == null || end == null) {
            return List.of();
        }
        
        return graph.findShortestPath(start, end);
    }
    
    public void highlightPath(List<Node> path) {
        // Reset all shapes to normal appearance
        for (Shape shape : drawingController.getCurrentDrawing().getShapes()) {
            if (shape instanceof Edge) {
                shape.setColor(Color.BLACK);
                shape.setStrokeWidth(1);
            } else if (shape instanceof Node) {
                shape.setColor(Color.BLUE);
            }
        }
        
        // Highlight nodes in the path
        for (Node node : path) {
            node.setColor(Color.RED);
        }
        
        // Highlight edges in the path
        for (int i = 0; i < path.size() - 1; i++) {
            Node current = path.get(i);
            Node next = path.get(i + 1);
            
            for (Edge edge : current.getEdges()) {
                if (edge.getOtherNode(current) == next) {
                    edge.setColor(Color.RED);
                    edge.setStrokeWidth(3);
                    break;
                }
            }
        }
        
        drawingController.updateCanvas();
    }
    
    public Graph getGraph() {
        return graph;
    }
}





