#!/bin/bash

# Download JavaFX if not present
if [ ! -d "javafx-sdk" ]; then
    echo "Downloading JavaFX SDK..."
    wget https://download2.gluonhq.com/openjfx/17.0.2/openjfx-17.0.2_linux-x64_bin-sdk.zip
    unzip openjfx-17.0.2_linux-x64_bin-sdk.zip
    mv javafx-sdk-17.0.2 javafx-sdk
    rm openjfx-17.0.2_linux-x64_bin-sdk.zip
fi

# Compile the project
mvn clean compile

# Run with JavaFX modules
java --module-path javafx-sdk/lib --add-modules javafx.controls,javafx.fxml -cp target/classes com.drawing.app.Main
