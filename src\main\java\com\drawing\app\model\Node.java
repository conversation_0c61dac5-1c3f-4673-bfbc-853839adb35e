package com.drawing.app.model;

import javafx.scene.paint.Color;
import java.util.ArrayList;
import java.util.List;

public class Node extends Circle {
    private String label;
    private List<Edge> edges = new ArrayList<>();
    
    public Node(double x, double y, double radius, Color color, double strokeWidth, String label) {
        super(x, y, radius, color, strokeWidth);
        this.label = label;
    }
    
    public String getLabel() {
        return label;
    }
    
    public void setLabel(String label) {
        this.label = label;
    }
    
    public void addEdge(Edge edge) {
        edges.add(edge);
    }
    
    public List<Edge> getEdges() {
        return edges;
    }
    
    @Override
    public String toString() {
        return "Node " + label;
    }
}