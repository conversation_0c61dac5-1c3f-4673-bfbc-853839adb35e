package com.drawing.app.persistence;

import com.drawing.app.model.Circle;
import com.drawing.app.model.Drawing;
import com.drawing.app.model.Line;
import com.drawing.app.model.Rectangle;
import com.drawing.app.model.Shape;
import javafx.scene.paint.Color;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class FileDrawingDAO implements DrawingDAO {
    private final String baseDirectory;
    
    public FileDrawingDAO(String baseDirectory) {
        this.baseDirectory = baseDirectory;
        createDirectoryIfNotExists();
    }
    
    public FileDrawingDAO() {
        this("drawings");
    }
    
    private void createDirectoryIfNotExists() {
        try {
            Path path = Paths.get(baseDirectory);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
            }
        } catch (IOException e) {
            System.err.println("Error creating drawings directory: " + e.getMessage());
        }
    }
    
    @Override
    public void save(Drawing drawing) {
        String filename = sanitizeFilename(drawing.getName()) + ".draw";
        Path filePath = Paths.get(baseDirectory, filename);
        
        try (PrintWriter writer = new PrintWriter(Files.newBufferedWriter(filePath))) {
            writer.println("DRAWING:" + drawing.getName());
            writer.println("SHAPES:" + drawing.getShapes().size());
            
            for (Shape shape : drawing.getShapes()) {
                if (shape instanceof Rectangle) {
                    Rectangle rect = (Rectangle) shape;
                    writer.printf(Locale.US, "RECTANGLE:%.2f:%.2f:%.2f:%.2f:%s:%.2f%n",
                        rect.getX(), rect.getY(), rect.getWidth(), rect.getHeight(),
                        colorToString(rect.getColor()), rect.getStrokeWidth());
                } else if (shape instanceof Circle) {
                    Circle circle = (Circle) shape;
                    writer.printf(Locale.US, "CIRCLE:%.2f:%.2f:%.2f:%s:%.2f%n",
                        circle.getX(), circle.getY(), circle.getRadius(),
                        colorToString(circle.getColor()), circle.getStrokeWidth());
                } else if (shape instanceof Line) {
                    Line line = (Line) shape;
                    writer.printf(Locale.US, "LINE:%.2f:%.2f:%.2f:%.2f:%s:%.2f%n",
                        line.getX(), line.getY(), line.getEndX(), line.getEndY(),
                        colorToString(line.getColor()), line.getStrokeWidth());
                }
            }
        } catch (IOException e) {
            System.err.println("Error saving drawing to file: " + e.getMessage());
        }
    }
    
    @Override
    public Drawing load(String name) {
        String filename = sanitizeFilename(name) + ".draw";
        Path filePath = Paths.get(baseDirectory, filename);
        Drawing drawing = new Drawing(name);
        
        if (!Files.exists(filePath)) {
            return drawing; // Return empty drawing if file doesn't exist
        }
        
        try (BufferedReader reader = Files.newBufferedReader(filePath)) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("DRAWING:")) {
                    String drawingName = line.substring(8);
                    drawing.setName(drawingName);
                } else if (line.startsWith("SHAPES:")) {
                    // Skip shape count line
                    continue;
                } else if (line.startsWith("RECTANGLE:")) {
                    Shape shape = parseRectangle(line);
                    if (shape != null) drawing.addShape(shape);
                } else if (line.startsWith("CIRCLE:")) {
                    Shape shape = parseCircle(line);
                    if (shape != null) drawing.addShape(shape);
                } else if (line.startsWith("LINE:")) {
                    Shape shape = parseLine(line);
                    if (shape != null) drawing.addShape(shape);
                }
            }
        } catch (IOException e) {
            System.err.println("Error loading drawing from file: " + e.getMessage());
        }
        
        return drawing;
    }
    
    @Override
    public List<String> getAllDrawingNames() {
        List<String> names = new ArrayList<>();
        
        try {
            Path dir = Paths.get(baseDirectory);
            if (Files.exists(dir)) {
                Files.list(dir)
                    .filter(path -> path.toString().endsWith(".draw"))
                    .forEach(path -> {
                        String filename = path.getFileName().toString();
                        String name = filename.substring(0, filename.length() - 5); // Remove .draw extension
                        names.add(name);
                    });
            }
        } catch (IOException e) {
            System.err.println("Error listing drawing files: " + e.getMessage());
        }
        
        return names;
    }
    
    @Override
    public void delete(String name) {
        String filename = sanitizeFilename(name) + ".draw";
        Path filePath = Paths.get(baseDirectory, filename);
        
        try {
            Files.deleteIfExists(filePath);
        } catch (IOException e) {
            System.err.println("Error deleting drawing file: " + e.getMessage());
        }
    }
    
    private String sanitizeFilename(String filename) {
        return filename.replaceAll("[^a-zA-Z0-9._-]", "_");
    }
    
    private String colorToString(Color color) {
        return String.format("#%02X%02X%02X",
            (int) (color.getRed() * 255),
            (int) (color.getGreen() * 255),
            (int) (color.getBlue() * 255));
    }
    
    private Color stringToColor(String colorString) {
        try {
            return Color.web(colorString);
        } catch (Exception e) {
            return Color.BLACK; // Default color if parsing fails
        }
    }
    
    private Rectangle parseRectangle(String line) {
        try {
            String[] parts = line.substring(10).split(":");
            double x = parseDouble(parts[0]);
            double y = parseDouble(parts[1]);
            double width = parseDouble(parts[2]);
            double height = parseDouble(parts[3]);
            Color color = stringToColor(parts[4]);
            double strokeWidth = parseDouble(parts[5]);
            return new Rectangle(x, y, width, height, color, strokeWidth);
        } catch (Exception e) {
            System.err.println("Error parsing rectangle: " + e.getMessage());
            return null;
        }
    }
    
    private Circle parseCircle(String line) {
        try {
            String[] parts = line.substring(7).split(":");
            double x = parseDouble(parts[0]);
            double y = parseDouble(parts[1]);
            double radius = parseDouble(parts[2]);
            Color color = stringToColor(parts[3]);
            double strokeWidth = parseDouble(parts[4]);
            return new Circle(x, y, radius, color, strokeWidth);
        } catch (Exception e) {
            System.err.println("Error parsing circle: " + e.getMessage());
            return null;
        }
    }

    private Line parseLine(String line) {
        try {
            String[] parts = line.substring(5).split(":");
            double x = parseDouble(parts[0]);
            double y = parseDouble(parts[1]);
            double endX = parseDouble(parts[2]);
            double endY = parseDouble(parts[3]);
            Color color = stringToColor(parts[4]);
            double strokeWidth = parseDouble(parts[5]);
            return new Line(x, y, endX, endY, color, strokeWidth);
        } catch (Exception e) {
            System.err.println("Error parsing line: " + e.getMessage());
            return null;
        }
    }

    private double parseDouble(String value) {
        // Handle both comma and dot decimal separators
        return Double.parseDouble(value.replace(',', '.'));
    }
}
