package com.drawing.app.model;

import javafx.scene.paint.Color;

public class Edge extends Line {
    private Node startNode;
    private Node endNode;
    private double weight;
    
    public Edge(Node startNode, Node endNode, Color color, double strokeWidth, double weight) {
        super(startNode.getX(), startNode.getY(), endNode.getX(), endNode.getY(), color, strokeWidth);
        this.startNode = startNode;
        this.endNode = endNode;
        this.weight = weight;
        
        // Add this edge to both nodes
        startNode.addEdge(this);
        endNode.addEdge(this);
    }
    
    public Node getStartNode() {
        return startNode;
    }
    
    public Node getEndNode() {
        return endNode;
    }
    
    public double getWeight() {
        return weight;
    }
    
    public void setWeight(double weight) {
        this.weight = weight;
    }
    
    public Node getOtherNode(Node node) {
        return node == startNode ? endNode : startNode;
    }
}