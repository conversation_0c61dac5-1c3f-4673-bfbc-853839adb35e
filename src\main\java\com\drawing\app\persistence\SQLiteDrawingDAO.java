package com.drawing.app.persistence;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

import com.drawing.app.model.Circle;
import com.drawing.app.model.Drawing;
import com.drawing.app.model.Line;
import com.drawing.app.model.Rectangle;
import com.drawing.app.model.Shape;

import javafx.scene.paint.Color;

public class SQLiteDrawingDAO implements DrawingDAO {
    private String dbUrl;
    
    public SQLiteDrawingDAO(String dbUrl) {
        this.dbUrl = dbUrl;
        initializeDatabase();
    }
    
    private void initializeDatabase() {
        try (Connection conn = DriverManager.getConnection(dbUrl)) {
            String createDrawingsTable = 
                "CREATE TABLE IF NOT EXISTS drawings (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "name TEXT UNIQUE NOT NULL, " +
                "created_date DATETIME DEFAULT CURRENT_TIMESTAMP" +
                ")  ";
            
            String createShapesTable = 
                "CREATE TABLE IF NOT EXISTS shapes (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "drawing_id INTEGER, " +
                "shape_type TEXT NOT NULL, " +
                "x REAL NOT NULL, " +
                "y REAL NOT NULL, " +
                "color TEXT NOT NULL, " +
                "stroke_width REAL NOT NULL, " +
                "param1 REAL, " +
                "param2 REAL, " +
                "param3 REAL, " +
                "param4 REAL, " +
                "FOREIGN KEY (drawing_id) REFERENCES drawings (id)" +
                ")";
            
            try (Statement stmt = conn.createStatement()) {
                stmt.execute(createDrawingsTable);
                stmt.execute(createShapesTable);
            }
        } catch (SQLException e) {
            System.err.println("Error initializing database: " + e.getMessage());
        }
    }
    
    @Override
    public void save(Drawing drawing) {
        try (Connection conn = DriverManager.getConnection(dbUrl)) {
            conn.setAutoCommit(false);
            
            // Insert or update drawing
            String insertDrawing = "INSERT OR REPLACE INTO drawings (name) VALUES (?)";
            int drawingId;
            
            try (PreparedStatement stmt = conn.prepareStatement(insertDrawing, Statement.RETURN_GENERATED_KEYS)) {
                stmt.setString(1, drawing.getName());
                stmt.executeUpdate();
                
                try (ResultSet rs = stmt.getGeneratedKeys()) {
                    if (rs.next()) {
                        drawingId = rs.getInt(1);
                    } else {
                        // Get existing ID
                        String selectId = "SELECT id FROM drawings WHERE name = ?";
                        try (PreparedStatement selectStmt = conn.prepareStatement(selectId)) {
                            selectStmt.setString(1, drawing.getName());
                            try (ResultSet selectRs = selectStmt.executeQuery()) {
                                if (selectRs.next()) {
                                    drawingId = selectRs.getInt(1);
                                } else {
                                    throw new SQLException("Could not get drawing ID");
                                }
                            }
                        }
                    }
                }
            }
            
            // Delete existing shapes
            String deleteShapes = "DELETE FROM shapes WHERE drawing_id = ?";
            try (PreparedStatement stmt = conn.prepareStatement(deleteShapes)) {
                stmt.setInt(1, drawingId);
                stmt.executeUpdate();
            }
            
            // Insert shapes
            String insertShape = "INSERT INTO shapes (drawing_id, shape_type, x, y, color, stroke_width, param1, param2, param3, param4) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            try (PreparedStatement stmt = conn.prepareStatement(insertShape)) {
                for (Shape shape : drawing.getShapes()) {
                    stmt.setInt(1, drawingId);
                    stmt.setString(2, shape.getClass().getSimpleName());
                    stmt.setDouble(3, shape.getX());
                    stmt.setDouble(4, shape.getY());
                    stmt.setString(5, colorToString(shape.getColor()));
                    stmt.setDouble(6, shape.getStrokeWidth());
                    
                    // Set shape-specific parameters
                    if (shape instanceof Rectangle) {
                        Rectangle rect = (Rectangle) shape;
                        stmt.setDouble(7, rect.getWidth());
                        stmt.setDouble(8, rect.getHeight());
                        stmt.setNull(9, Types.REAL);
                        stmt.setNull(10, Types.REAL);
                    } else if (shape instanceof Circle) {
                        Circle circle = (Circle) shape;
                        stmt.setDouble(7, circle.getRadius());
                        stmt.setNull(8, Types.REAL);
                        stmt.setNull(9, Types.REAL);
                        stmt.setNull(10, Types.REAL);
                    } else if (shape instanceof Line) {
                        Line line = (Line) shape;
                        stmt.setDouble(7, line.getEndX());
                        stmt.setDouble(8, line.getEndY());
                        stmt.setNull(9, Types.REAL);
                        stmt.setNull(10, Types.REAL);
                    }
                    
                    stmt.executeUpdate();
                }
            }
            
            conn.commit();
        } catch (SQLException e) {
            System.err.println("Error saving drawing: " + e.getMessage());
        }
    }
    
    @Override
    public Drawing load(String name) {
        Drawing drawing = new Drawing(name);
        
        try (Connection conn = DriverManager.getConnection(dbUrl)) {
            // Get drawing ID
            String selectDrawing = "SELECT id FROM drawings WHERE name = ?";
            int drawingId;
            
            try (PreparedStatement stmt = conn.prepareStatement(selectDrawing)) {
                stmt.setString(1, name);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        drawingId = rs.getInt(1);
                    } else {
                        return drawing; // Empty drawing if not found
                    }
                }
            }
            
            // Load shapes
            String selectShapes = 
                "SELECT shape_type, x, y, color, stroke_width, param1, param2, param3, param4 " +
                "FROM shapes WHERE drawing_id = ?";
            
            try (PreparedStatement stmt = conn.prepareStatement(selectShapes)) {
                stmt.setInt(1, drawingId);
                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        String shapeType = rs.getString("shape_type");
                        double x = rs.getDouble("x");
                        double y = rs.getDouble("y");
                        Color color = stringToColor(rs.getString("color"));
                        double strokeWidth = rs.getDouble("stroke_width");
                        
                        Shape shape = null;
                        switch (shapeType) {
                            case "Rectangle":
                                double width = rs.getDouble("param1");
                                double height = rs.getDouble("param2");
                                shape = new Rectangle(x, y, width, height, color, strokeWidth);
                                break;
                            case "Circle":
                                double radius = rs.getDouble("param1");
                                shape = new Circle(x, y, radius, color, strokeWidth);
                                break;
                            case "Line":
                                double endX = rs.getDouble("param1");
                                double endY = rs.getDouble("param2");
                                shape = new Line(x, y, endX, endY, color, strokeWidth);
                                break;
                        }
                        
                        if (shape != null) {
                            drawing.addShape(shape);
                        }
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("Error loading drawing: " + e.getMessage());
        }
        
        return drawing;
    }
    
    @Override
    public List<String> getAllDrawingNames() {
        List<String> names = new ArrayList<>();
        
        try (Connection conn = DriverManager.getConnection(dbUrl)) {
            String selectNames = "SELECT name FROM drawings ORDER BY created_date DESC";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(selectNames)) {
                while (rs.next()) {
                    names.add(rs.getString("name"));
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting drawing names: " + e.getMessage());
        }
        
        return names;
    }
    
    @Override
    public void delete(String name) {
        try (Connection conn = DriverManager.getConnection(dbUrl)) {
            String deleteDrawing = "DELETE FROM drawings WHERE name = ?";
            try (PreparedStatement stmt = conn.prepareStatement(deleteDrawing)) {
                stmt.setString(1, name);
                stmt.executeUpdate();
            }
        } catch (SQLException e) {
            System.err.println("Error deleting drawing: " + e.getMessage());
        }
    }
    
    private String colorToString(Color color) {
        return String.format("#%02X%02X%02X",
            (int) (color.getRed() * 255),
            (int) (color.getGreen() * 255),
            (int) (color.getBlue() * 255));
    }
    
    private Color stringToColor(String colorString) {
        return Color.web(colorString);
    }
}
