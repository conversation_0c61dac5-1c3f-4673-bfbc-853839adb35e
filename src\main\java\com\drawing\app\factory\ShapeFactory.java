package com.drawing.app.factory;

import com.drawing.app.model.*;
import javafx.scene.paint.Color;

// Factory Pattern
public class ShapeFactory {
    
    public enum ShapeType {
        RECTANGLE, CIRCLE, LINE
    }
    
    public static Shape createShape(ShapeType type, double x, double y, double... params) {
        Color defaultColor = Color.BLACK;
        double defaultStrokeWidth = 2.0;
        
        switch (type) {
            case RECTANGLE:
                double width = params.length > 0 ? params[0] : 50;
                double height = params.length > 1 ? params[1] : 30;
                return new Rectangle(x, y, width, height, defaultColor, defaultStrokeWidth);
                
            case CIRCLE:
                double radius = params.length > 0 ? params[0] : 25;
                return new Circle(x, y, radius, defaultColor, defaultStrokeWidth);
                
            case LINE:
                double endX = params.length > 0 ? params[0] : x + 50;
                double endY = params.length > 1 ? params[1] : y + 50;
                return new Line(x, y, endX, endY, defaultColor, defaultStrokeWidth);
                
            default:
                throw new IllegalArgumentException("Unknown shape type: " + type);
        }
    }
    
    public static Shape createShape(ShapeType type, double x, double y, Color color, double strokeWidth, double... params) {
        Shape shape = createShape(type, x, y, params);
        shape.setColor(color);
        shape.setStrokeWidth(strokeWidth);
        return shape;
    }
}
