package com.drawing.app.model;

import java.util.ArrayList;
import java.util.List;

public class Drawing {
    private List<Shape> shapes;
    private String name;
    
    public Drawing() {
        this.shapes = new ArrayList<>();
        this.name = "Untitled";
    }
    
    public Drawing(String name) {
        this.shapes = new ArrayList<>();
        this.name = name;
    }
    
    public void addShape(Shape shape) {
        shapes.add(shape);
    }
    
    public void removeShape(Shape shape) {
        shapes.remove(shape);
    }
    
    public List<Shape> getShapes() {
        return new ArrayList<>(shapes);
    }
    
    public void clear() {
        shapes.clear();
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public int getShapeCount() {
        return shapes.size();
    }
}
