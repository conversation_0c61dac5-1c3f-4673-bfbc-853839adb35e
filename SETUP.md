# JavaFX Drawing Application Setup Guide

## Prerequisites
- Java 11 or higher
- Maven 3.6 or higher

## Running the Application

### Method 1: Using JavaFX Maven Plugin (Recommended)
\`\`\`bash
mvn clean javafx:run
\`\`\`

### Method 2: Using Run Scripts
1. Make the run script executable (Linux/Mac):
   \`\`\`bash
   chmod +x run.sh
   ./run.sh
   \`\`\`

2. Or run the batch file (Windows):
   \`\`\`cmd
   run.bat
   \`\`\`

### Method 3: Manual Setup
1. Download JavaFX SDK from https://openjfx.io/
2. Extract to a folder (e.g., `javafx-sdk`)
3. Compile the project:
   \`\`\`bash
   mvn clean compile
   \`\`\`
4. Run with JavaFX modules:
   \`\`\`bash
   java --module-path /path/to/javafx-sdk/lib --add-modules javafx.controls,javafx.fxml -cp target/classes com.drawing.app.Main
   \`\`\`

## Building Executable JAR
\`\`\`bash
mvn clean package
\`\`\`

## IDE Setup

### IntelliJ IDEA
1. Go to File → Project Structure → Libraries
2. Add JavaFX SDK lib folder as a library
3. In Run Configuration, add VM options:
   \`\`\`
   --module-path /path/to/javafx-sdk/lib --add-modules javafx.controls,javafx.fxml
   \`\`\`

### Eclipse
1. Add JavaFX SDK to the build path
2. In Run Configuration → Arguments → VM arguments:
   \`\`\`
   --module-path /path/to/javafx-sdk/lib --add-modules javafx.controls,javafx.fxml
   \`\`\`

### VS Code
1. Install Extension Pack for Java
2. Add to `.vscode/launch.json`:
   \`\`\`json
   {
       "type": "java",
       "name": "Launch Main",
       "request": "launch",
       "mainClass": "com.drawing.app.Main",
       "vmArgs": "--module-path /path/to/javafx-sdk/lib --add-modules javafx.controls,javafx.fxml"
   }
   \`\`\`

## Troubleshooting

### Common Issues:
1. **JavaFX runtime components are missing**: Use one of the methods above
2. **Module not found**: Ensure JavaFX SDK path is correct
3. **Permission denied**: Make run scripts executable with `chmod +x`

### Database Location:
- SQLite database files will be created in the project root directory
- Log files will be created in the project root directory
