package com.drawing.app.model;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

public class Circle extends Shape {
    private double radius;
    
    public Circle(double x, double y, double radius, Color color, double strokeWidth) {
        super(x, y, color, strokeWidth);
        this.radius = Math.abs(radius);
    }
    
    @Override
    protected void draw(GraphicsContext gc) {
        gc.strokeOval(x - radius, y - radius, radius * 2, radius * 2);
    }
      @Override
    public boolean contains(double px, double py) {
        double dx = px - x;
        double dy = py - y;
        return Math.sqrt(dx * dx + dy * dy) <= radius;
    }
    
    @Override
    public double getCenterX() {
        return x;
    }
    
    @Override
    public double getCenterY() {
        return y;
    }
    
    @Override
    protected void drawSelectionHandles(GraphicsContext gc) {
        gc.setStroke(Color.BLUE);
        gc.setLineWidth(1);
        
        // Draw selection circle
        gc.strokeOval(x - radius - 2, y - radius - 2, (radius + 2) * 2, (radius + 2) * 2);
        
        // Draw handles
        double handleSize = 6;
        gc.setFill(Color.BLUE);
        
        // Top
        gc.fillRect(x - handleSize/2, y - radius - handleSize/2, handleSize, handleSize);
        // Right
        gc.fillRect(x + radius - handleSize/2, y - handleSize/2, handleSize, handleSize);
        // Bottom
        gc.fillRect(x - handleSize/2, y + radius - handleSize/2, handleSize, handleSize);
        // Left
        gc.fillRect(x - radius - handleSize/2, y - handleSize/2, handleSize, handleSize);
        
        // Rotation handle
        gc.setFill(Color.GREEN);
        gc.fillOval(x - handleSize/2, y - radius - 20 - handleSize/2, handleSize, handleSize);
        gc.strokeLine(x, y - radius, x, y - radius - 20);
    }
    
    public void updateRadius(double newRadius) {
        this.radius = Math.abs(newRadius);
    }
    
    public double getRadius() { return radius; }
    public void setRadius(double radius) { this.radius = Math.abs(radius); }
}
