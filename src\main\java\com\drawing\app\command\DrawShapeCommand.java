package com.drawing.app.command;

import com.drawing.app.model.Drawing;
import com.drawing.app.model.Shape;

public class DrawShapeCommand implements Command {
    private Drawing drawing;
    private Shape shape;
    
    public DrawShapeCommand(Drawing drawing, Shape shape) {
        this.drawing = drawing;
        this.shape = shape;
    }
    
    @Override
    public void execute() {
        drawing.addShape(shape);
    }
    
    @Override
    public void undo() {
        drawing.removeShape(shape);
    }
}
