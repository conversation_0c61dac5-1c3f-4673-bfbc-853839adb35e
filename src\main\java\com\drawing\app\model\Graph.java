package com.drawing.app.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.PriorityQueue;
import java.util.Set;

public class Graph {
    private List<Node> nodes = new ArrayList<>();
    private List<Edge> edges = new ArrayList<>();
    
    public void addNode(Node node) {
        nodes.add(node);
    }
    
    public void addEdge(Edge edge) {
        edges.add(edge);
    }
    
    public List<Node> getNodes() {
        return nodes;
    }
    
    public List<Edge> getEdges() {
        return edges;
    }
    
    public Node findNodeByLabel(String label) {
        for (Node node : nodes) {
            if (node.getLabel().equals(label)) {
                return node;
            }
        }
        return null;
    }
    
    // <PERSON><PERSON><PERSON>'s algorithm for shortest path
    public List<Node> findShortestPath(Node start, Node end) {
        Map<Node, Double> distances = new HashMap<>();
        Map<Node, Node> previousNodes = new HashMap<>();
        PriorityQueue<NodeDistance> queue = new PriorityQueue<>();
        Set<Node> visited = new HashSet<>();
        
        // Initialize distances
        for (Node node : nodes) {
            distances.put(node, Double.MAX_VALUE);
        }
        distances.put(start, 0.0);
        queue.add(new NodeDistance(start, 0.0));
        
        while (!queue.isEmpty()) {
            NodeDistance current = queue.poll();
            Node currentNode = current.node;
            
            if (currentNode == end) {
                break;
            }
            
            if (visited.contains(currentNode)) {
                continue;
            }
            
            visited.add(currentNode);
            
            for (Edge edge : currentNode.getEdges()) {
                Node neighbor = edge.getOtherNode(currentNode);
                double newDistance = distances.get(currentNode) + edge.getWeight();
                
                if (newDistance < distances.get(neighbor)) {
                    distances.put(neighbor, newDistance);
                    previousNodes.put(neighbor, currentNode);
                    queue.add(new NodeDistance(neighbor, newDistance));
                }
            }
        }
        
        // Reconstruct path
        List<Node> path = new ArrayList<>();
        Node current = end;
        
        if (previousNodes.get(current) == null && current != start) {
            return path; // No path exists
        }
        
        while (current != null) {
            path.add(0, current);
            current = previousNodes.get(current);
        }
        
        return path;
    }
    
    // Helper class for Dijkstra's algorithm
    private static class NodeDistance implements Comparable<NodeDistance> {
        Node node;
        double distance;
        
        NodeDistance(Node node, double distance) {
            this.node = node;
            this.distance = distance;
        }
        
        @Override
        public int compareTo(NodeDistance other) {
            return Double.compare(this.distance, other.distance);
        }
    }
}