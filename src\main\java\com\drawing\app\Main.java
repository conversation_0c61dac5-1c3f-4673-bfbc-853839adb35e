package com.drawing.app;

import javafx.application.Application;
import javafx.scene.Scene;
import javafx.stage.Stage;
import com.drawing.app.view.MainView;

public class Main extends Application {
    
    @Override
    public void start(Stage primaryStage) {
        MainView mainView = new MainView();
        Scene scene = new Scene(mainView.getRoot(), 1000, 700);
        
        primaryStage.setTitle("Drawing Application - MASI Mini Projet");
        primaryStage.setScene(scene);
        primaryStage.show();
    }
    
    public static void main(String[] args) {
        launch(args);
    }
}
